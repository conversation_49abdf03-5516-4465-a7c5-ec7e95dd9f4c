{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function Home() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // Redirect to portfolio view\n    router.push('/portfolio');\n  }, [router]);\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,OAAO;AACT", "debugId": null}}]}