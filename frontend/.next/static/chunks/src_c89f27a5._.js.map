{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { format, formatDistanceToNow, parseISO } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function getRiskLabel(riskBand: string): string {\n  switch (riskBand.toLowerCase()) {\n    case 'green':\n      return 'Low';\n    case 'yellow':\n      return 'Medium';\n    case 'red':\n      return 'High';\n    default:\n      return 'Unknown';\n  }\n}\n\nexport function formatDate(dateString: string): string {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM dd, yyyy');\n  } catch {\n    return 'Invalid date';\n  }\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(amount);\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,QAAgB;IAC3C,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,IAAI;QACF,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-brand-600 text-white hover:bg-brand-700\",\n        secondary: \"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        destructive: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n        outline: \"text-gray-900 border-gray-300\",\n        success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        danger: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,+KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;QACV;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Shield, Users, BarChart3 } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\nimport { cn } from '@/lib/utils';\n\nexport default function Navigation() {\n  const pathname = usePathname();\n  const isActive = (path: string) => pathname === path;\n\n  return (\n    <nav className=\"sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 hover:no-underline\">\n              <div className=\"w-8 h-8 bg-brand-600 rounded-lg flex items-center justify-center\">\n                <Shield className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-800\">\n                Credit Chakra\n              </span>\n            </Link>\n\n            <div className=\"flex items-center space-x-1\">\n              <Link\n                href=\"/\"\n                className={cn(\n                  \"flex items-center space-x-2 px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-200 border\",\n                  isActive('/')\n                    ? \"bg-brand-50 text-brand-700 border-brand-200 hover:bg-brand-100 hover:text-brand-800\"\n                    : \"bg-transparent text-gray-600 border-transparent hover:bg-gray-50 hover:text-brand-600\"\n                )}\n              >\n                <BarChart3 className=\"w-4 h-4\" />\n                <span>Analytics</span>\n              </Link>\n\n              <Link\n                href=\"/msmes\"\n                className={cn(\n                  \"flex items-center space-x-2 px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-200 border\",\n                  isActive('/msmes')\n                    ? \"bg-brand-50 text-brand-700 border-brand-200 hover:bg-brand-100 hover:text-brand-800\"\n                    : \"bg-transparent text-gray-600 border-transparent hover:bg-gray-50 hover:text-brand-600\"\n                )}\n              >\n                <Users className=\"w-4 h-4\" />\n                <span>Portfolio</span>\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Badge variant=\"secondary\" className=\"px-3 py-1 text-xs font-semibold\">\n              Credit Manager\n            </Badge>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAC,OAAiB,aAAa;IAEhD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6GACA,SAAS,OACL,wFACA;;0DAGN,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6GACA,SAAS,YACL,wFACA;;0DAGN,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnF;GAxDwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}]}